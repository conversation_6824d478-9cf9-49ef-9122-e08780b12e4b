[2025-07-28 05:07:16] local.INFO: Starting webhook dispatch for action: booking.booking_form_submitted {"timestamp":"2025-07-28T05:07:16.657880Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"entity_type":"Booking","entity_id":10,"status":"dispatching"} 
[2025-07-28 05:07:19] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2026 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-28T05:07:19.040515Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2342.0,"user_id":84,"entity_id":10,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-28T05:07:16.698262Z","data":{"event_id":5,"name":"Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-30","time":"13:30","selected_location":{"type":"phone","value":"8617555736","display":"Phone call"},"custom_fields":["{{calendar_events.enter_your_business_name}}"],"custom_fields_value":["sit"],"updated_at":"2025-07-28T05:07:16.000000Z","created_at":"2025-07-28T05:07:16.000000Z","id":10,"form_data":{"name":"Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-30","time":"13:30","selected_location":{"type":"phone","value":"8617555736","display":"Phone call"},"custom_fields":["{{calendar_events.enter_your_business_name}}"],"custom_fields_value":["sit"],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2026 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-28 05:07:19] local.WARNING: Webhook dispatch completed for action: booking.booking_form_submitted. Success: 0, Failed: 1 {"timestamp":"2025-07-28T05:07:19.041641Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"status":"completed","total_modules":1,"successful_modules":0,"failed_modules":1,"modules":["OMX FLOW"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2026 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"}}} 
[2025-07-28 05:25:29] local.INFO: Starting webhook dispatch for action: booking.event_created {"timestamp":"2025-07-28T05:25:29.478308Z","source":"crm_webhook_system","action":"booking.event_created","user_id":84,"entity_type":null,"entity_id":null,"status":"dispatching"} 
[2025-07-28 05:25:33] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2020 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-28T05:25:33.750019Z","source":"crm_webhook_system","action":"booking.event_created","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":4266.0,"user_id":84,"entity_id":6,"entity_type":null,"request_payload":{"action":"booking.event_created","timestamp":"2025-07-28T05:25:29.483575Z","data":{"id":6,"title":"OMX Flow Live","start_date":"2025-07-28 05:25:10","end_date":"2026-07-28 05:25:10","duration":60,"booking_per_slot":1,"minimum_notice":44,"description":"OK","location":"phone","meet_link":"8617555736","physical_address":null,"custom_fields":[{"unique_key":"{{calendar_events.enter_gst_number}}","type":"number","label":"Enter GST NUmber","options":[],"is_required":0},{"unique_key":"{{calendar_events.who_are_your_key_competitors_and_what_differentiates_you}}","type":"textarea","label":"Who are your key competitors, and what differentiates you?","options":[],"is_required":0},{"unique_key":"{{calendar_events.enter_your_business_name}}","type":"text","label":"Enter Your Business Name","options":[],"is_required":0}],"date_override":null,"created_by":84,"slots_created":2928,"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2020 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-28 05:25:33] local.WARNING: Webhook dispatch completed for action: booking.event_created. Success: 0, Failed: 1 {"timestamp":"2025-07-28T05:25:33.750533Z","source":"crm_webhook_system","action":"booking.event_created","user_id":84,"status":"completed","total_modules":1,"successful_modules":0,"failed_modules":1,"modules":["OMX FLOW"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2020 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"}}} 
[2025-07-28 05:28:05] local.INFO: Starting webhook dispatch for action: booking.booking_form_submitted {"timestamp":"2025-07-28T05:28:05.026778Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"entity_type":"Booking","entity_id":11,"status":"dispatching"} 
[2025-07-28 05:28:07] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2037 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-28T05:28:07.126975Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2084.0,"user_id":84,"entity_id":11,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-28T05:28:05.042975Z","data":{"event_id":6,"name":"Parichay Singha Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-29","time":"14:30","selected_location":{"type":"phone","value":"8617555736","display":"Phone call"},"custom_fields":[],"custom_fields_value":[],"updated_at":"2025-07-28T05:28:04.000000Z","created_at":"2025-07-28T05:28:04.000000Z","id":11,"form_data":{"name":"Parichay Singha Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-29","time":"14:30","selected_location":{"type":"phone","value":"8617555736","display":"Phone call"},"custom_fields":[],"custom_fields_value":[],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2037 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-28 05:28:07] local.WARNING: Webhook dispatch completed for action: booking.booking_form_submitted. Success: 0, Failed: 1 {"timestamp":"2025-07-28T05:28:07.127408Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"status":"completed","total_modules":1,"successful_modules":0,"failed_modules":1,"modules":["OMX FLOW"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2037 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"}}} 
[2025-07-28 05:32:53] local.INFO: Starting webhook dispatch for action: booking.event_created {"timestamp":"2025-07-28T05:32:53.009288Z","source":"crm_webhook_system","action":"booking.event_created","user_id":84,"entity_type":null,"entity_id":null,"status":"dispatching"} 
[2025-07-28 05:32:55] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2045 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-28T05:32:55.103649Z","source":"crm_webhook_system","action":"booking.event_created","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2090.0,"user_id":84,"entity_id":7,"entity_type":null,"request_payload":{"action":"booking.event_created","timestamp":"2025-07-28T05:32:53.014132Z","data":{"id":7,"title":"Live Chat Bot AI Live","start_date":"2025-07-28 05:32:42","end_date":"2026-07-28 05:32:42","duration":60,"booking_per_slot":1,"minimum_notice":29,"description":"OK This is a Best","location":"phone","meet_link":"Siliguri","physical_address":null,"custom_fields":[{"unique_key":"{{calendar_events.enter_your_business_name}}","type":"text","label":"Enter Your Business Name","options":[],"is_required":0},{"unique_key":"{{calendar_events.enter_gst_number}}","type":"number","label":"Enter GST NUmber","options":[],"is_required":0},{"unique_key":"{{calendar_events.who_are_your_key_competitors_and_what_differentiates_you}}","type":"textarea","label":"Who are your key competitors, and what differentiates you?","options":[],"is_required":0}],"date_override":"[\"2025-07-29T04:30\"]","created_by":84,"slots_created":2928,"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2045 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-28 05:32:55] local.WARNING: Webhook dispatch completed for action: booking.event_created. Success: 0, Failed: 1 {"timestamp":"2025-07-28T05:32:55.104057Z","source":"crm_webhook_system","action":"booking.event_created","user_id":84,"status":"completed","total_modules":1,"successful_modules":0,"failed_modules":1,"modules":["OMX FLOW"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2045 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"}}} 
[2025-07-28 05:34:04] local.INFO: Starting webhook dispatch for action: booking.booking_form_submitted {"timestamp":"2025-07-28T05:34:04.067008Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"entity_type":"Booking","entity_id":12,"status":"dispatching"} 
[2025-07-28 05:34:06] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2022 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-28T05:34:06.123302Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2048.0,"user_id":84,"entity_id":12,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-28T05:34:04.074889Z","data":{"event_id":7,"name":"Jatndea Nath Singha","email":"<EMAIL>","phone":"9932313212","date":"2025-07-30","time":"13:00","selected_location":{"type":"phone","value":"Siliguri","display":"Phone call"},"custom_fields":["{{calendar_events.enter_your_business_name}}"],"custom_fields_value":["Smart Internz"],"updated_at":"2025-07-28T05:34:04.000000Z","created_at":"2025-07-28T05:34:04.000000Z","id":12,"form_data":{"name":"Jatndea Nath Singha","email":"<EMAIL>","phone":"9932313212","date":"2025-07-30","time":"13:00","selected_location":{"type":"phone","value":"Siliguri","display":"Phone call"},"custom_fields":["{{calendar_events.enter_your_business_name}}"],"custom_fields_value":["Smart Internz"],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2022 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-28 05:34:06] local.WARNING: Webhook dispatch completed for action: booking.booking_form_submitted. Success: 0, Failed: 1 {"timestamp":"2025-07-28T05:34:06.124093Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"status":"completed","total_modules":1,"successful_modules":0,"failed_modules":1,"modules":["OMX FLOW"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2022 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"}}} 
[2025-07-28 06:08:31] local.INFO: Starting webhook dispatch for action: booking.event_created {"timestamp":"2025-07-28T06:08:31.509498Z","source":"crm_webhook_system","action":"booking.event_created","user_id":84,"entity_type":null,"entity_id":null,"status":"dispatching"} 
[2025-07-28 06:08:33] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2017 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-28T06:08:33.612881Z","source":"crm_webhook_system","action":"booking.event_created","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2097.0,"user_id":84,"entity_id":8,"entity_type":null,"request_payload":{"action":"booking.event_created","timestamp":"2025-07-28T06:08:31.515922Z","data":{"id":8,"title":"This is the best","start_date":"2025-07-28 06:08:17","end_date":"2026-07-28 06:08:17","duration":60,"booking_per_slot":1,"minimum_notice":39,"description":"<p>OK, This is the Best&nbsp; &nbsp; sdkbknknlafelkalk</p>","location":"phone","meet_link":"8617555736","physical_address":null,"custom_fields":[{"unique_key":"{{calendar_events.enter_gst_number}}","type":"number","label":"Enter GST NUmber","options":[],"is_required":true}],"date_override":null,"created_by":84,"slots_created":2928,"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2017 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-28 06:08:33] local.WARNING: Webhook dispatch completed for action: booking.event_created. Success: 0, Failed: 1 {"timestamp":"2025-07-28T06:08:33.614214Z","source":"crm_webhook_system","action":"booking.event_created","user_id":84,"status":"completed","total_modules":1,"successful_modules":0,"failed_modules":1,"modules":["OMX FLOW"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2017 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"}}} 
[2025-07-28 06:11:15] local.INFO: Starting webhook dispatch for action: booking.booking_form_submitted {"timestamp":"2025-07-28T06:11:15.291445Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"entity_type":"Booking","entity_id":13,"status":"dispatching"} 
[2025-07-28 06:11:17] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2026 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-28T06:11:17.363755Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2066.0,"user_id":84,"entity_id":13,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-28T06:11:15.298130Z","data":{"event_id":8,"name":"Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-31","time":"13:30","selected_location":{"type":"phone","value":"8617555736","display":"Phone call"},"custom_fields":["{{calendar_events.enter_gst_number}}"],"custom_fields_value":["89654235689"],"updated_at":"2025-07-28T06:11:15.000000Z","created_at":"2025-07-28T06:11:15.000000Z","id":13,"form_data":{"name":"Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-31","time":"13:30","selected_location":{"type":"phone","value":"8617555736","display":"Phone call"},"custom_fields":["{{calendar_events.enter_gst_number}}"],"custom_fields_value":["89654235689"],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2026 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-28 06:11:17] local.WARNING: Webhook dispatch completed for action: booking.booking_form_submitted. Success: 0, Failed: 1 {"timestamp":"2025-07-28T06:11:17.365017Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"status":"completed","total_modules":1,"successful_modules":0,"failed_modules":1,"modules":["OMX FLOW"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2026 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"}}} 
[2025-07-28 07:40:49] local.INFO: Starting webhook dispatch for action: crm.lead_created {"timestamp":"2025-07-28T07:40:49.966504Z","source":"crm_webhook_system","action":"crm.lead_created","user_id":84,"entity_type":"Lead","entity_id":12,"status":"dispatching"} 
[2025-07-28 07:40:52] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2022 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-28T07:40:52.076884Z","source":"crm_webhook_system","action":"crm.lead_created","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2105.0,"user_id":84,"entity_id":12,"entity_type":"Lead","request_payload":{"action":"crm.lead_created","timestamp":"2025-07-28T07:40:49.971499Z","data":{"name":"Parichay Singha","email":"<EMAIL>","phone":"+918617555736","subject":"Lead from Parichay Singha","user_id":"85","pipeline_id":27,"stage_id":105,"created_by":84,"date":"2025-07-28","date_of_birth":null,"type":null,"status":"active","opportunity_info":null,"opportunity_description":null,"opportunity_source":null,"lead_value":null,"next_follow_up_date":null,"labels":[],"updated_at":"2025-07-28T07:40:49.000000Z","created_at":"2025-07-28T07:40:49.000000Z","id":12,"stage":{"id":105,"name":"Draft","pipeline_id":27,"created_by":84,"order":1,"created_at":"2025-07-28T07:33:40.000000Z","updated_at":"2025-07-28T07:33:40.000000Z"},"pipeline":{"id":27,"name":"Bot Flow Parichay","created_by":84,"is_deleted":0,"created_at":"2025-07-28T07:33:40.000000Z","updated_at":"2025-07-28T07:33:40.000000Z"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2022 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-28 07:40:52] local.WARNING: Webhook dispatch completed for action: crm.lead_created. Success: 0, Failed: 1 {"timestamp":"2025-07-28T07:40:52.077953Z","source":"crm_webhook_system","action":"crm.lead_created","user_id":84,"status":"completed","total_modules":1,"successful_modules":0,"failed_modules":1,"modules":["OMX FLOW"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2022 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"}}} 
[2025-07-28 10:50:21] local.INFO: Starting webhook dispatch for action: crm.lead_created {"timestamp":"2025-07-28T10:50:21.082943Z","source":"crm_webhook_system","action":"crm.lead_created","user_id":84,"entity_type":"Lead","entity_id":14,"status":"dispatching"} 
[2025-07-28 10:50:23] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2048 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-28T10:50:23.873903Z","source":"crm_webhook_system","action":"crm.lead_created","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2620.0,"user_id":84,"entity_id":14,"entity_type":"Lead","request_payload":{"action":"crm.lead_created","timestamp":"2025-07-28T10:50:21.253986Z","data":{"name":"Jatndea Nath Singha","email":"<EMAIL>","phone":"9932313212","subject":"New Contact","user_id":"84","pipeline_id":28,"stage_id":108,"created_by":84,"date":"2025-07-28","date_of_birth":"2025-07-17","type":null,"status":"active","opportunity_info":null,"opportunity_description":null,"opportunity_source":null,"lead_value":null,"next_follow_up_date":null,"contact_type":"Lead","tags":"OMX","postal_code":"734429","city":"DARJILING","state":"West Bengal","country":"India","business_name":"sit","business_gst":"sit","business_state":"West Bengal","business_postal_code":"734429","business_address":"GANDAGOL JOTE, PANITANKI,DULAL JOTE,KHARIBARI,GARJEELING,734429

naxalbari","dnd_settings":"{\"all\":false,\"emails\":false,\"whatsapp\":false,\"sms\":false,\"calls\":false}","updated_at":"2025-07-28T10:50:20.000000Z","created_at":"2025-07-28T10:50:20.000000Z","id":14,"stage":{"id":108,"name":"Draft One","pipeline_id":28,"created_by":84,"order":1,"created_at":"2025-07-28T10:20:29.000000Z","updated_at":"2025-07-28T10:20:29.000000Z"},"pipeline":{"id":28,"name":"AI","created_by":84,"is_deleted":0,"created_at":"2025-07-28T10:20:29.000000Z","updated_at":"2025-07-28T10:20:29.000000Z"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2048 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-28 10:50:23] local.WARNING: Webhook dispatch completed for action: crm.lead_created. Success: 0, Failed: 1 {"timestamp":"2025-07-28T10:50:23.876671Z","source":"crm_webhook_system","action":"crm.lead_created","user_id":84,"status":"completed","total_modules":1,"successful_modules":0,"failed_modules":1,"modules":["OMX FLOW"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2048 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"}}} 
[2025-07-28 10:54:50] local.INFO: Starting webhook dispatch for action: crm.lead_stage_changed {"timestamp":"2025-07-28T10:54:50.700760Z","source":"crm_webhook_system","action":"crm.lead_stage_changed","user_id":84,"entity_type":"Lead","entity_id":14,"status":"dispatching"} 
[2025-07-28 10:54:52] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2022 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-28T10:54:52.879356Z","source":"crm_webhook_system","action":"crm.lead_stage_changed","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2159.0,"user_id":84,"entity_id":14,"entity_type":"Lead","request_payload":{"action":"crm.lead_stage_changed","timestamp":"2025-07-28T10:54:50.720284Z","data":{"id":14,"name":"Jatndea Nath Singha","contact_type":"Lead","tags":"OMX","postal_code":"734429","city":"DARJILING","state":"West Bengal","country":"India","business_name":"sit","business_gst":"sit","business_state":"West Bengal","business_postal_code":"734429","business_address":"GANDAGOL JOTE, PANITANKI,DULAL JOTE,KHARIBARI,GARJEELING,734429

naxalbari","dnd_settings":"{\"all\":false,\"emails\":false,\"whatsapp\":false,\"sms\":false,\"calls\":false}","email":"<EMAIL>","phone":"9932313212","date_of_birth":"2025-07-17","type":null,"status":"active","opportunity_info":null,"opportunity_description":null,"opportunity_source":null,"lead_value":null,"subject":"New Contact","user_id":84,"pipeline_id":28,"stage_id":108,"sources":[],"products":[],"notes":null,"labels":[],"order":0,"created_by":84,"is_deleted":0,"is_active":1,"is_converted":0,"date":"2025-07-28","next_follow_up_date":null,"created_at":"2025-07-28T10:50:20.000000Z","updated_at":"2025-07-28T10:50:20.000000Z","stage":{"id":108,"name":"Draft One","pipeline_id":28,"created_by":84,"order":1,"created_at":"2025-07-28T10:20:29.000000Z","updated_at":"2025-07-28T10:20:29.000000Z"},"users":[{"id":84,"name":"parichay  Bot flow","email":"<EMAIL>","email_verified_at":"2025-07-21T06:08:10.000000Z","plan":10,"plan_expire_date":"2026-07-21","requested_plan":0,"trial_plan":0,"trial_expire_date":null,"type":"company","system_admin_company_id":null,"company_name":null,"company_description":null,"module_permissions":{"crm":["create lead","create deal","create form builder","create contract","create pipeline","create stage","create source","create label","view crm dashboard","view lead","view deal","view form builder","view contract","view pipeline","view stage","view source","view label","delete lead","delete deal","delete form builder","delete contract","delete pipeline","delete stage","delete source","delete label","manage lead","edit lead","manage deal","edit deal","manage form builder","edit form builder","manage contract","edit contract","manage pipeline","edit pipeline","manage stage","edit stage","manage source","edit source","manage label","edit label"],"hrm":["create employee","create set salary","create pay slip","create leave","create attendance","create training","create award","create branch","create department","create designation","create document type","view hrm dashboard","view employee","view set salary","view pay slip","view leave","view attendance","view training","view award","view branch","view department","view designation","view document type","delete employee","delete set salary","delete pay slip","delete leave","delete attendance","delete training","delete award","delete branch","delete department","delete designation","delete document type","manage employee","edit employee","manage set salary","edit set salary","manage pay slip","edit pay slip","manage leave","edit leave","manage attendance","edit attendance","manage training","edit training","manage award","edit award","manage branch","edit branch","manage department","edit department","manage designation","edit designation","manage document type","edit document type"],"account":["create customer","create vender","create invoice","create bill","create revenue","create payment","create proposal","create goal","create credit note","create debit note","create bank account","create bank transfer","create transaction","create chart of account","create journal entry","create assets","create constant custom field","view account dashboard","view customer","view vender","view invoice","view bill","view revenue","view payment","view proposal","view goal","view credit note","view debit note","view bank account","view bank transfer","view transaction","view chart of account","view journal entry","view assets","view constant custom field","view report","delete customer","delete vender","delete invoice","delete bill","delete revenue","delete payment","delete proposal","delete goal","delete credit note","delete debit note","delete bank account","delete bank transfer","delete transaction","delete chart of account","delete journal entry","delete assets","delete constant custom field","manage customer","edit customer","manage vender","edit vender","manage invoice","edit invoice","manage bill","edit bill","manage revenue","edit revenue","manage payment","edit payment","manage proposal","edit proposal","manage goal","edit goal","manage credit note","edit credit note","manage debit note","edit debit note","manage bank account","edit bank account","manage bank transfer","edit bank transfer","manage transaction","edit transaction","manage chart of account","edit chart of account","manage journal entry","edit journal entry","manage assets","edit assets","manage constant custom field","edit constant custom field","manage report"],"project":["create project","create project task","create timesheet","create bug report","create milestone","create project stage","create project task stage","create project expense","create activity","create bug status","view project dashboard","view project","view project task","view timesheet","view bug report","view milestone","view project stage","view project task stage","view project expense","view activity","view bug status","delete project","delete project task","delete timesheet","delete bug report","delete milestone","delete project stage","delete project task stage","delete project expense","delete activity","delete bug status","manage project","edit project","manage project task","edit project task","manage timesheet","edit timesheet","manage bug report","edit bug report","manage milestone","edit milestone","manage project stage","edit project stage","manage project task stage","edit project task stage","manage project expense","edit project expense","manage activity","edit activity","manage bug status","edit bug status"],"pos":["create warehouse","create purchase","create quotation","create pos","create barcode","create product","create product category","create product unit","view pos dashboard","view warehouse","view purchase","view quotation","view pos","view product","view product category","view product unit","delete warehouse","delete purchase","delete quotation","delete pos","delete product","delete product category","delete product unit","manage warehouse","edit warehouse","manage purchase","edit purchase","manage quotation","edit quotation","manage pos","edit pos","manage product","edit product","manage product category","edit product category","manage product unit","edit product unit"],"support":["create support","view support dashboard","view support","delete support","manage support","edit support","reply support"],"user_management":["create user","create client","view user","view client","delete user","delete client","manage user","edit user","manage client","edit client"],"booking":["create booking","create appointment","create appointment booking","create calendar event","view booking dashboard","view booking","show booking","view appointment","show appointment","view appointment booking","show appointment booking","view calendar event","show calendar event","delete booking","delete appointment","delete appointment booking","delete calendar event","manage booking","edit booking","manage appointment","edit appointment","manage appointment booking","edit appointment booking","manage calendar event","edit calendar event"],"omx_flow":["access omx flow","whatsapp_flows","whatsapp_orders","campaigns","templates","chatbot"],"personal_tasks":["create personal task","create personal task comment","create personal task file","create personal task checklist","view personal task","delete personal task","delete personal task comment","delete personal task file","delete personal task checklist","manage personal task","edit personal task","edit personal task comment","edit personal task checklist","manage personal task time tracking"],"automatish":["access automatish"]},"storage_limit":0.0,"avatar":"","messenger_color":"#2180f3","lang":"en","default_pipeline":28,"active_status":0,"delete_status":1,"mode":"light","dark_mode":0,"is_disable":1,"is_enable_login":1,"is_active":1,"referral_code":0,"used_referral_code":0,"commission_amount":0,"last_login_at":null,"created_by":7,"created_at":"2025-07-21T06:08:10.000000Z","updated_at":"2025-07-28T10:38:18.000000Z","is_email_verified":0,"profile":"http://localhost:8000/storage/avatar.png","pivot":{"lead_id":14,"user_id":84}}],"pipeline":{"id":28,"name":"AI","created_by":84,"is_deleted":0,"created_at":"2025-07-28T10:20:29.000000Z","updated_at":"2025-07-28T10:20:29.000000Z"},"old_stage_id":108,"new_stage_id":"109","triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2022 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-28 10:54:52] local.WARNING: Webhook dispatch completed for action: crm.lead_stage_changed. Success: 0, Failed: 1 {"timestamp":"2025-07-28T10:54:52.882235Z","source":"crm_webhook_system","action":"crm.lead_stage_changed","user_id":84,"status":"completed","total_modules":1,"successful_modules":0,"failed_modules":1,"modules":["OMX FLOW"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2022 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"}}} 
[2025-07-28 11:14:24] local.INFO: Starting webhook dispatch for action: crm.lead_created {"timestamp":"2025-07-28T11:14:24.950947Z","source":"crm_webhook_system","action":"crm.lead_created","user_id":84,"entity_type":"Lead","entity_id":15,"status":"dispatching"} 
[2025-07-28 11:14:27] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2025 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-28T11:14:27.119165Z","source":"crm_webhook_system","action":"crm.lead_created","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2154.0,"user_id":84,"entity_id":15,"entity_type":"Lead","request_payload":{"action":"crm.lead_created","timestamp":"2025-07-28T11:14:24.964803Z","data":{"name":"Raja  Ram","email":"<EMAIL>","phone":"+918654895689","subject":"Lead from Raja  Ram","user_id":"86","pipeline_id":28,"stage_id":108,"created_by":84,"date":"2025-07-28","date_of_birth":null,"type":null,"status":"active","opportunity_info":null,"opportunity_description":null,"opportunity_source":null,"lead_value":null,"next_follow_up_date":null,"contact_type":"Lead","tags":null,"postal_code":null,"city":null,"state":null,"country":null,"business_name":null,"business_gst":null,"business_state":null,"business_postal_code":null,"business_address":null,"dnd_settings":null,"updated_at":"2025-07-28T11:14:24.000000Z","created_at":"2025-07-28T11:14:24.000000Z","id":15,"stage":{"id":108,"name":"Draft One","pipeline_id":28,"created_by":84,"order":1,"created_at":"2025-07-28T10:20:29.000000Z","updated_at":"2025-07-28T10:20:29.000000Z"},"pipeline":{"id":28,"name":"AI","created_by":84,"is_deleted":0,"created_at":"2025-07-28T10:20:29.000000Z","updated_at":"2025-07-28T10:20:29.000000Z"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2025 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-28 11:14:27] local.WARNING: Webhook dispatch completed for action: crm.lead_created. Success: 0, Failed: 1 {"timestamp":"2025-07-28T11:14:27.121966Z","source":"crm_webhook_system","action":"crm.lead_created","user_id":84,"status":"completed","total_modules":1,"successful_modules":0,"failed_modules":1,"modules":["OMX FLOW"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2025 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"}}} 
[2025-07-28 11:14:45] local.INFO: Starting webhook dispatch for action: crm.lead_stage_changed {"timestamp":"2025-07-28T11:14:45.346214Z","source":"crm_webhook_system","action":"crm.lead_stage_changed","user_id":84,"entity_type":"Lead","entity_id":15,"status":"dispatching"} 
[2025-07-28 11:14:47] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2029 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-28T11:14:47.503656Z","source":"crm_webhook_system","action":"crm.lead_stage_changed","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2136.0,"user_id":84,"entity_id":15,"entity_type":"Lead","request_payload":{"action":"crm.lead_stage_changed","timestamp":"2025-07-28T11:14:45.367745Z","data":{"id":15,"name":"Raja  Ram","contact_type":"Lead","tags":null,"postal_code":null,"city":null,"state":null,"country":null,"business_name":null,"business_gst":null,"business_state":null,"business_postal_code":null,"business_address":null,"dnd_settings":null,"email":"<EMAIL>","phone":"+918654895689","date_of_birth":null,"type":null,"status":"active","opportunity_info":null,"opportunity_description":null,"opportunity_source":null,"lead_value":null,"subject":"Lead from Raja  Ram","user_id":86,"pipeline_id":28,"stage_id":108,"sources":[],"products":[],"notes":null,"labels":[],"order":0,"created_by":84,"is_deleted":0,"is_active":1,"is_converted":0,"date":"2025-07-28","next_follow_up_date":null,"created_at":"2025-07-28T11:14:24.000000Z","updated_at":"2025-07-28T11:14:24.000000Z","stage":{"id":108,"name":"Draft One","pipeline_id":28,"created_by":84,"order":1,"created_at":"2025-07-28T10:20:29.000000Z","updated_at":"2025-07-28T10:20:29.000000Z"},"users":[{"id":84,"name":"parichay  Bot flow","email":"<EMAIL>","email_verified_at":"2025-07-21T06:08:10.000000Z","plan":10,"plan_expire_date":"2026-07-21","requested_plan":0,"trial_plan":0,"trial_expire_date":null,"type":"company","system_admin_company_id":null,"company_name":null,"company_description":null,"module_permissions":{"crm":["create lead","create deal","create form builder","create contract","create pipeline","create stage","create source","create label","view crm dashboard","view lead","view deal","view form builder","view contract","view pipeline","view stage","view source","view label","delete lead","delete deal","delete form builder","delete contract","delete pipeline","delete stage","delete source","delete label","manage lead","edit lead","manage deal","edit deal","manage form builder","edit form builder","manage contract","edit contract","manage pipeline","edit pipeline","manage stage","edit stage","manage source","edit source","manage label","edit label"],"hrm":["create employee","create set salary","create pay slip","create leave","create attendance","create training","create award","create branch","create department","create designation","create document type","view hrm dashboard","view employee","view set salary","view pay slip","view leave","view attendance","view training","view award","view branch","view department","view designation","view document type","delete employee","delete set salary","delete pay slip","delete leave","delete attendance","delete training","delete award","delete branch","delete department","delete designation","delete document type","manage employee","edit employee","manage set salary","edit set salary","manage pay slip","edit pay slip","manage leave","edit leave","manage attendance","edit attendance","manage training","edit training","manage award","edit award","manage branch","edit branch","manage department","edit department","manage designation","edit designation","manage document type","edit document type"],"account":["create customer","create vender","create invoice","create bill","create revenue","create payment","create proposal","create goal","create credit note","create debit note","create bank account","create bank transfer","create transaction","create chart of account","create journal entry","create assets","create constant custom field","view account dashboard","view customer","view vender","view invoice","view bill","view revenue","view payment","view proposal","view goal","view credit note","view debit note","view bank account","view bank transfer","view transaction","view chart of account","view journal entry","view assets","view constant custom field","view report","delete customer","delete vender","delete invoice","delete bill","delete revenue","delete payment","delete proposal","delete goal","delete credit note","delete debit note","delete bank account","delete bank transfer","delete transaction","delete chart of account","delete journal entry","delete assets","delete constant custom field","manage customer","edit customer","manage vender","edit vender","manage invoice","edit invoice","manage bill","edit bill","manage revenue","edit revenue","manage payment","edit payment","manage proposal","edit proposal","manage goal","edit goal","manage credit note","edit credit note","manage debit note","edit debit note","manage bank account","edit bank account","manage bank transfer","edit bank transfer","manage transaction","edit transaction","manage chart of account","edit chart of account","manage journal entry","edit journal entry","manage assets","edit assets","manage constant custom field","edit constant custom field","manage report"],"project":["create project","create project task","create timesheet","create bug report","create milestone","create project stage","create project task stage","create project expense","create activity","create bug status","view project dashboard","view project","view project task","view timesheet","view bug report","view milestone","view project stage","view project task stage","view project expense","view activity","view bug status","delete project","delete project task","delete timesheet","delete bug report","delete milestone","delete project stage","delete project task stage","delete project expense","delete activity","delete bug status","manage project","edit project","manage project task","edit project task","manage timesheet","edit timesheet","manage bug report","edit bug report","manage milestone","edit milestone","manage project stage","edit project stage","manage project task stage","edit project task stage","manage project expense","edit project expense","manage activity","edit activity","manage bug status","edit bug status"],"pos":["create warehouse","create purchase","create quotation","create pos","create barcode","create product","create product category","create product unit","view pos dashboard","view warehouse","view purchase","view quotation","view pos","view product","view product category","view product unit","delete warehouse","delete purchase","delete quotation","delete pos","delete product","delete product category","delete product unit","manage warehouse","edit warehouse","manage purchase","edit purchase","manage quotation","edit quotation","manage pos","edit pos","manage product","edit product","manage product category","edit product category","manage product unit","edit product unit"],"support":["create support","view support dashboard","view support","delete support","manage support","edit support","reply support"],"user_management":["create user","create client","view user","view client","delete user","delete client","manage user","edit user","manage client","edit client"],"booking":["create booking","create appointment","create appointment booking","create calendar event","view booking dashboard","view booking","show booking","view appointment","show appointment","view appointment booking","show appointment booking","view calendar event","show calendar event","delete booking","delete appointment","delete appointment booking","delete calendar event","manage booking","edit booking","manage appointment","edit appointment","manage appointment booking","edit appointment booking","manage calendar event","edit calendar event"],"omx_flow":["access omx flow","whatsapp_flows","whatsapp_orders","campaigns","templates","chatbot"],"personal_tasks":["create personal task","create personal task comment","create personal task file","create personal task checklist","view personal task","delete personal task","delete personal task comment","delete personal task file","delete personal task checklist","manage personal task","edit personal task","edit personal task comment","edit personal task checklist","manage personal task time tracking"],"automatish":["access automatish"]},"storage_limit":0.0,"avatar":"","messenger_color":"#2180f3","lang":"en","default_pipeline":28,"active_status":0,"delete_status":1,"mode":"light","dark_mode":0,"is_disable":1,"is_enable_login":1,"is_active":1,"referral_code":0,"used_referral_code":0,"commission_amount":0,"last_login_at":null,"created_by":7,"created_at":"2025-07-21T06:08:10.000000Z","updated_at":"2025-07-28T11:13:27.000000Z","is_email_verified":0,"profile":"http://localhost:8000/storage/avatar.png","pivot":{"lead_id":15,"user_id":84}},{"id":86,"name":"Gungun Rani","email":"<EMAIL>","email_verified_at":"2025-07-27T18:41:43.000000Z","plan":null,"plan_expire_date":null,"requested_plan":0,"trial_plan":0,"trial_expire_date":null,"type":"employee","system_admin_company_id":null,"company_name":null,"company_description":null,"module_permissions":[],"storage_limit":0.0,"avatar":"avatar.png","messenger_color":"#2180f3","lang":"en","default_pipeline":null,"active_status":0,"delete_status":1,"mode":"light","dark_mode":0,"is_disable":1,"is_enable_login":1,"is_active":1,"referral_code":0,"used_referral_code":0,"commission_amount":0,"last_login_at":null,"created_by":84,"created_at":"2025-07-27T18:41:43.000000Z","updated_at":"2025-07-27T18:41:43.000000Z","is_email_verified":0,"profile":"http://localhost:8000/storage/avatar.png","pivot":{"lead_id":15,"user_id":86}}],"pipeline":{"id":28,"name":"AI","created_by":84,"is_deleted":0,"created_at":"2025-07-28T10:20:29.000000Z","updated_at":"2025-07-28T10:20:29.000000Z"},"old_stage_id":108,"new_stage_id":"117","triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2029 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-28 11:14:47] local.WARNING: Webhook dispatch completed for action: crm.lead_stage_changed. Success: 0, Failed: 1 {"timestamp":"2025-07-28T11:14:47.504849Z","source":"crm_webhook_system","action":"crm.lead_stage_changed","user_id":84,"status":"completed","total_modules":1,"successful_modules":0,"failed_modules":1,"modules":["OMX FLOW"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2029 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"}}} 
[2025-07-28 11:17:02] local.INFO: Starting webhook dispatch for action: crm.lead_created {"timestamp":"2025-07-28T11:17:02.903570Z","source":"crm_webhook_system","action":"crm.lead_created","user_id":84,"entity_type":"Lead","entity_id":16,"status":"dispatching"} 
[2025-07-28 11:17:05] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2045 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-28T11:17:05.023410Z","source":"crm_webhook_system","action":"crm.lead_created","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2111.0,"user_id":84,"entity_id":16,"entity_type":"Lead","request_payload":{"action":"crm.lead_created","timestamp":"2025-07-28T11:17:02.912151Z","data":{"name":"Gungun Rani","email":"<EMAIL>","phone":"+915895656869","subject":"Lead from Gungun Rani","user_id":"85","pipeline_id":28,"stage_id":108,"created_by":84,"date":"2025-07-28","date_of_birth":null,"type":null,"status":"active","opportunity_info":null,"opportunity_description":null,"opportunity_source":"other","lead_value":null,"next_follow_up_date":null,"contact_type":"Lead","tags":null,"postal_code":null,"city":null,"state":null,"country":null,"business_name":null,"business_gst":null,"business_state":null,"business_postal_code":null,"business_address":null,"dnd_settings":null,"updated_at":"2025-07-28T11:17:02.000000Z","created_at":"2025-07-28T11:17:02.000000Z","id":16,"stage":{"id":108,"name":"Draft One","pipeline_id":28,"created_by":84,"order":1,"created_at":"2025-07-28T10:20:29.000000Z","updated_at":"2025-07-28T10:20:29.000000Z"},"pipeline":{"id":28,"name":"AI","created_by":84,"is_deleted":0,"created_at":"2025-07-28T10:20:29.000000Z","updated_at":"2025-07-28T10:20:29.000000Z"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2045 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-28 11:17:05] local.WARNING: Webhook dispatch completed for action: crm.lead_created. Success: 0, Failed: 1 {"timestamp":"2025-07-28T11:17:05.024743Z","source":"crm_webhook_system","action":"crm.lead_created","user_id":84,"status":"completed","total_modules":1,"successful_modules":0,"failed_modules":1,"modules":["OMX FLOW"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2045 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"}}} 
[2025-07-28 13:26:20] local.INFO: Starting webhook dispatch for action: booking.appointment_scheduled {"timestamp":"2025-07-28T13:26:20.889754Z","source":"crm_webhook_system","action":"booking.appointment_scheduled","user_id":84,"entity_type":null,"entity_id":null,"status":"dispatching"} 
[2025-07-28 13:26:24] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2017 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-28T13:26:24.377884Z","source":"crm_webhook_system","action":"booking.appointment_scheduled","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":3477.0,"user_id":84,"entity_id":17641,"entity_type":null,"request_payload":{"action":"booking.appointment_scheduled","timestamp":"2025-07-28T13:26:20.900579Z","data":{"id":17641,"title":"AI Bot Flow Builder","event_date":"2025-07-27","time_slots":"15:30","time_zone":"GMT","location":"phone","location_value":"8617555736","event":{"id":3,"title":"AI Bot Flow Builder","start_date":"2025-07-27T16:36:33.000000Z","end_date":"2026-07-27T16:36:33.000000Z","duration":60,"booking_per_slot":1,"minimum_notice":0,"minimum_notice_value":0,"minimum_notice_unit":"minutes","description":"OK","location":"phone","locations_data":"\"[{\\\"type\\\":\\\"phone\\\",\\\"value\\\":\\\"8617555736\\\",\\\"display\\\":\\\"Phone call\\\"}]\"","custom_redirect_url":null,"meet_link":"8617555736","physical_address":null,"require_name":true,"require_email":true,"require_phone":false,"custom_field":null,"custom_field_value":null,"weekly_availability":null,"created_by":84,"assigned_staff_id":87,"status":"active","date_range_type":"calendar_days","date_range_days":30,"date_range_start":null,"date_range_end":null,"created_at":"2025-07-27T16:36:33.000000Z","updated_at":"2025-07-27T18:45:43.000000Z","date_override":null,"custom_fields":null},"status":"scheduled","triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2017 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-28 13:26:24] local.WARNING: Webhook dispatch completed for action: booking.appointment_scheduled. Success: 0, Failed: 1 {"timestamp":"2025-07-28T13:26:24.379040Z","source":"crm_webhook_system","action":"booking.appointment_scheduled","user_id":84,"status":"completed","total_modules":1,"successful_modules":0,"failed_modules":1,"modules":["OMX FLOW"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2017 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"}}} 
[2025-07-28 13:27:56] local.INFO: Starting webhook dispatch for action: booking.appointment_scheduled {"timestamp":"2025-07-28T13:27:56.126849Z","source":"crm_webhook_system","action":"booking.appointment_scheduled","user_id":84,"entity_type":null,"entity_id":null,"status":"dispatching"} 
[2025-07-28 13:27:58] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2024 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-28T13:27:58.208964Z","source":"crm_webhook_system","action":"booking.appointment_scheduled","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2073.0,"user_id":84,"entity_id":17642,"entity_type":null,"request_payload":{"action":"booking.appointment_scheduled","timestamp":"2025-07-28T13:27:56.135944Z","data":{"id":17642,"title":"AI Bot Flow Builder","event_date":"2025-07-27","time_slots":"15:30","time_zone":"GMT","location":"phone","location_value":"8617555736","event":{"id":3,"title":"AI Bot Flow Builder","start_date":"2025-07-27T16:36:33.000000Z","end_date":"2026-07-27T16:36:33.000000Z","duration":60,"booking_per_slot":1,"minimum_notice":0,"minimum_notice_value":0,"minimum_notice_unit":"minutes","description":"OK","location":"phone","locations_data":"\"[{\\\"type\\\":\\\"phone\\\",\\\"value\\\":\\\"8617555736\\\",\\\"display\\\":\\\"Phone call\\\"}]\"","custom_redirect_url":null,"meet_link":"8617555736","physical_address":null,"require_name":true,"require_email":true,"require_phone":false,"custom_field":null,"custom_field_value":null,"weekly_availability":null,"created_by":84,"assigned_staff_id":87,"status":"active","date_range_type":"calendar_days","date_range_days":30,"date_range_start":null,"date_range_end":null,"created_at":"2025-07-27T16:36:33.000000Z","updated_at":"2025-07-27T18:45:43.000000Z","date_override":null,"custom_fields":null},"status":"scheduled","triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2024 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-28 13:27:58] local.WARNING: Webhook dispatch completed for action: booking.appointment_scheduled. Success: 0, Failed: 1 {"timestamp":"2025-07-28T13:27:58.209394Z","source":"crm_webhook_system","action":"booking.appointment_scheduled","user_id":84,"status":"completed","total_modules":1,"successful_modules":0,"failed_modules":1,"modules":["OMX FLOW"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2024 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"}}} 
[2025-07-28 15:53:07] local.INFO: Starting webhook dispatch for action: booking.appointment_scheduled {"timestamp":"2025-07-28T15:53:07.193612Z","source":"crm_webhook_system","action":"booking.appointment_scheduled","user_id":84,"entity_type":null,"entity_id":null,"status":"dispatching"} 
[2025-07-28 15:53:12] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2032 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-28T15:53:12.743017Z","source":"crm_webhook_system","action":"booking.appointment_scheduled","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":5476.0,"user_id":84,"entity_id":17643,"entity_type":null,"request_payload":{"action":"booking.appointment_scheduled","timestamp":"2025-07-28T15:53:07.267014Z","data":{"id":17643,"title":"OMX Flow Live","event_date":"2025-07-28","time_slots":"16:00","time_zone":"GMT","location":"phone","location_value":"8617555736","event":{"id":6,"title":"OMX Flow Live","start_date":"2025-07-28T05:25:10.000000Z","end_date":"2026-07-28T05:25:10.000000Z","duration":60,"booking_per_slot":1,"minimum_notice":43,"minimum_notice_value":43,"minimum_notice_unit":"minutes","description":"OKcz","location":"phone","locations_data":"\"[{\\\"type\\\":\\\"phone\\\",\\\"value\\\":\\\"8617555736\\\",\\\"display\\\":\\\"Phone call\\\"}]\"","custom_redirect_url":null,"meet_link":"8617555736","physical_address":null,"require_name":true,"require_email":true,"require_phone":false,"custom_field":null,"custom_field_value":null,"weekly_availability":null,"created_by":84,"assigned_staff_id":null,"status":"active","date_range_type":"indefinitely","date_range_days":null,"date_range_start":null,"date_range_end":null,"created_at":"2025-07-28T05:25:10.000000Z","updated_at":"2025-07-28T06:26:35.000000Z","date_override":null,"custom_fields":null},"status":"scheduled","triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2032 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-28 15:53:12] local.WARNING: Webhook dispatch completed for action: booking.appointment_scheduled. Success: 0, Failed: 1 {"timestamp":"2025-07-28T15:53:12.744906Z","source":"crm_webhook_system","action":"booking.appointment_scheduled","user_id":84,"status":"completed","total_modules":1,"successful_modules":0,"failed_modules":1,"modules":["OMX FLOW"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2032 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"}}} 
[2025-07-28 16:10:07] local.INFO: Starting webhook dispatch for action: booking.event_created {"timestamp":"2025-07-28T16:10:07.886415Z","source":"crm_webhook_system","action":"booking.event_created","user_id":84,"entity_type":null,"entity_id":null,"status":"dispatching"} 
[2025-07-28 16:10:09] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2033 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-28T16:10:09.984269Z","source":"crm_webhook_system","action":"booking.event_created","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2091.0,"user_id":84,"entity_id":9,"entity_type":null,"request_payload":{"action":"booking.event_created","timestamp":"2025-07-28T16:10:07.892966Z","data":{"id":9,"title":"Parichay Demo Event","start_date":"2025-07-28 21:39:00","end_date":"2025-07-29 21:39:00","duration":60,"booking_per_slot":1,"minimum_notice":30,"description":"ok","location":"in_person","meet_link":null,"physical_address":"Siliguri","custom_fields":[{"type":"contact_type","label":"Contact Type"}],"date_override":null,"created_by":84,"slots_created":16,"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2033 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-28 16:10:09] local.WARNING: Webhook dispatch completed for action: booking.event_created. Success: 0, Failed: 1 {"timestamp":"2025-07-28T16:10:09.985603Z","source":"crm_webhook_system","action":"booking.event_created","user_id":84,"status":"completed","total_modules":1,"successful_modules":0,"failed_modules":1,"modules":["OMX FLOW"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2033 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"}}} 
