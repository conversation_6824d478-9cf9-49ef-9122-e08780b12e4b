<?php

namespace App\Http\Controllers;

use App\Models\CalendarEvent;
use App\Models\EventWeeklyAvailability;
use App\Http\Controllers\AppointmentController;
use App\Models\Appointment;
use App\Models\AppointmentBooking;
use App\Models\Booking;
use App\Services\SlotGeneratorService;
use App\Services\CrmWebhookDispatcher;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class CalendarEventController extends Controller
{
    /**
     * Check if user has calendar event permission based on pricing plan
     */
    private function hasCalendarEventPermission($permission)
    {
        $user = Auth::user();
        
        // System admin and staff have all permissions
        if ($user->type === 'system admin' || $user->type === 'staff') {
            return true;
        }
        
        // For company users, check pricing plan permissions
        if ($user->type === 'company') {
            return $user->hasModulePermission('booking', $permission);
        }
        
        return false;
    }

    public function store(Request $request)
    {
        // Check permission based on pricing plan
        if (!$this->hasCalendarEventPermission('create calendar event')) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized - Calendar event module not available in your plan.'
            ], 403);
        }

        Log::info("Request" , $request->toArray());
        // Validate request
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'duration' => 'nullable|integer|min:1|max:1440',
            'booking_per_slot' => 'nullable|integer|min:1|max:100',
            'minimum_notice' => 'nullable|integer|min:0',
            'description' => 'nullable|string',
            'location' => 'nullable|in:in_person,zoom,skype,meet,others',
            'meet_link' => 'nullable|url',
            'physical_address' => 'nullable|string',
            'custom_field' => 'nullable|string|max:255',
            'custom_field_value' => 'nullable|string',
            'custom_fields' => 'nullable|array',
            'custom_fields.*.type' => 'nullable|string|max:255',
            'custom_fields.*.label' => 'nullable|string|max:255',
            'availability' => 'nullable|array',
            'date_override' => 'nullable|array',
            'date_override.*' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            DB::beginTransaction();
            \Log::info('Incoming request data:', $request->all());
            // Process custom fields (field type and label only, no values)
            $customFields = [];

            if ($request->has('custom_fields') && is_array($request->custom_fields)) {
                $customFields = array_filter($request->custom_fields, function($field) {
                    return isset($field['type']) && isset($field['label']) && !empty($field['type']) && !empty($field['label']);
                });
            }

            // Create the calendar event
            $calendarEvent = CalendarEvent::create([
                'title' => $request->title,
                'start_date' => $request->start_date,
                'end_date' => $request->end_date,
                'duration' => $request->duration,
                'booking_per_slot' => $request->booking_per_slot ?? 1,
                'minimum_notice' => $request->minimum_notice ?? 0,
                'description' => $request->description,
                'location' => $request->location,
                'meet_link' => $request->meet_link,
                'physical_address' => $request->physical_address,
                'custom_field' => $request->custom_field, // Single field for backward compatibility
                'custom_field_value' => null, // No values stored, will be used during booking
                'custom_fields' => !empty($customFields) ? $customFields : null, // Multiple field names only
                'date_override' => !empty($request->input('date_override')) ? json_encode($request->input('date_override')) : null,// Store date overrides as JSON
                'created_by' => Auth::id()
            ]);
           

            // Save weekly availability
            if ($request->has('availability')) {
                $weeklyAvailability = $request->input('availability');
                \Log::info('Weekly availability data:', $weeklyAvailability);

                foreach ($weeklyAvailability as $day => $dayData) {
                    \Log::info("Processing day: $day", $dayData);

                    if (isset($dayData['enabled']) && $dayData['enabled']) {
                        if (isset($dayData['slots']) && is_array($dayData['slots'])) {
                            foreach ($dayData['slots'] as $slot) {
                                \Log::info("Creating slot for $day:", $slot);

                                EventWeeklyAvailability::create([
                                    'calendar_event_id' => $calendarEvent->id,
                                    'day_of_week' => $day,
                                    'start_time' => $slot['start'],
                                    'end_time' => $slot['end']
                                ]);
                            }
                        }
                    }
                }
            }

            // Generate appointment booking slots based on weekly availability
            try {
                $slotGenerator = new SlotGeneratorService();
                $slotsCreated = $slotGenerator->generateSlotsForEvent($calendarEvent);
                \Log::info("Generated {$slotsCreated} appointment slots for event ID: {$calendarEvent->id}");
            } catch (\Exception $e) {
                \Log::error("Failed to generate slots for event ID: {$calendarEvent->id} - " . $e->getMessage());
                // Don't fail the entire event creation if slot generation fails
                $slotsCreated = 0;
            }

            DB::commit();

            // Dispatch event created webhook
            try {
                $webhookDispatcher = new CrmWebhookDispatcher();
                $eventData = [
                    'id' => $calendarEvent->id,
                    'title' => $calendarEvent->title,
                    'start_date' => $calendarEvent->start_date,
                    'end_date' => $calendarEvent->end_date,
                    'duration' => $calendarEvent->duration,
                    'booking_per_slot' => $calendarEvent->booking_per_slot,
                    'minimum_notice' => $calendarEvent->minimum_notice,
                    'description' => $calendarEvent->description,
                    'location' => $calendarEvent->location,
                    'meet_link' => $calendarEvent->meet_link,
                    'physical_address' => $calendarEvent->physical_address,
                    'custom_fields' => $calendarEvent->custom_fields,
                    'date_override' => $calendarEvent->date_override,
                    'created_by' => $calendarEvent->created_by,
                    'slots_created' => $slotsCreated
                ];
                $webhookDispatcher->dispatchEventCreated($eventData);
            } catch (\Exception $e) {
                Log::error('Calendar event creation webhook dispatch failed: ' . $e->getMessage());
                // Don't fail the main operation due to webhook issues
            }

            return response()->json([
                'success' => true,
                'message' => 'Event created successfully',
                'data' => $calendarEvent,
                'slots_created' => $slotsCreated
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('Error creating calendar event: ' . $e->getMessage()); // Log the error
            
            return response()->json([
                'success' => false,
                'message' => 'Error creating calendar event: ' . $e->getMessage()
            ], 500);
        }
    }

    public function index()
    {
        // Check permission based on pricing plan
        if (!$this->hasCalendarEventPermission('view calendar event')) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized - Calendar event module not available in your plan.'
            ], 403);
        }
        
        $events = CalendarEvent::where('created_by', Auth::id())->get();

        // Simple foreach to get bookings
        foreach ($events as $event) {
            $bookings = Booking::where('event_id', $event->id)->get();
            $event->bookings = $bookings;
            $event->booking_count = $bookings->count();
        }

        return response()->json([
            'success' => true,
            'data' => $events
        ]);
    }

    public function show($id)
    {
        // Check permission based on pricing plan
        if (!$this->hasCalendarEventPermission('show calendar event')) {
            return redirect()->back()->with('error', 'Unauthorized - Calendar event module not available in your plan.');
        }

        try {
            $event = CalendarEvent::where('created_by', Auth::id())
                ->with(['weeklyAvailability', 'bookings'])
                ->findOrFail($id);

            return view('tasks.copy_event', [
                'event' => $event
            ]);
        } catch (ModelNotFoundException $e) {
            return redirect()->route('calendar-events.index')->with('error', 'Event not found.');
        } catch (\Exception $e) {
            return redirect()->route('calendar-events.index')->with('error', 'Error loading event details: ' . $e->getMessage());
        }
    }

    /**
     * Get event data for editing (AJAX endpoint)
     */
    public function getEventData($id)
    {
        // Check permission based on pricing plan
        if (!$this->hasCalendarEventPermission('view calendar event')) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized - Calendar event module not available in your plan.'
            ], 403);
        }

        try {
            $event = CalendarEvent::where('created_by', Auth::id())
                ->findOrFail($id);

            return response()->json([
                'success' => true,
                'data' => $event
            ]);
        } catch (ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Event not found.'
            ], 404);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error loading event details: ' . $e->getMessage()
            ], 500);
        }
    }
    
    public function view($id)
    {
        // Check permission based on pricing plan
        if (!$this->hasCalendarEventPermission('show calendar event')) {
            return redirect()->back()->with('error', 'Unauthorized - Calendar event module not available in your plan.');
        }
        
        try {
            $event = CalendarEvent::where('created_by', Auth::id())
                ->with('weeklyAvailability')
                ->findOrFail($id);
 
            return view('copy_event', [
                'event' => $event
            ]);
        } catch (ModelNotFoundException $e) {
            return redirect()->route('calendar-events.index')->with('error', 'Event not found.');
        } catch (\Exception $e) {
            return redirect()->route('calendar-events.index')->with('error', 'Error loading event details: ' . $e->getMessage());
        }
    }

    /**
     * Public view for sharing event links (no authentication required)
     */
    public function publicView($id)
    {
        try {
            $event = CalendarEvent::with(['weeklyAvailability', 'bookings'])->findOrFail($id);

            return view('tasks.copy_event', [
                'event' => $event
            ]);
        } catch (ModelNotFoundException $e) {
            abort(404, 'Event not found.');
        } catch (\Exception $e) {
            abort(500, 'Error loading event details.');
        }
    }

    public function update(Request $request, $id)
    {
        // Check permission based on pricing plan
        if (!$this->hasCalendarEventPermission('edit calendar event')) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized - Calendar event module not available in your plan.'
            ], 403);
        }
        
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'duration' => 'nullable|integer|min:1|max:1440',
            'booking_per_slot' => 'nullable|integer|min:1|max:100',
            'minimum_notice' => 'nullable|integer|min:0',
            'description' => 'nullable|string',
            'location' => 'nullable|in:in_person,zoom,skype,meet,others',
            'meet_link' => 'nullable|url',
            'physical_address' => 'nullable|string',
            'custom_field' => 'nullable|string',
            'custom_field_value' => 'nullable|string',
            'date_override' => 'nullable|array', 
            'date_override.*' => 'date_format:Y-m-d\TH:i',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            DB::beginTransaction();

            $calendarEvent = CalendarEvent::where('created_by', Auth::id())->findOrFail($id);

            $updateData = [
                'title' => $request->title,
                'start_date' => $request->start_date,
                'end_date' => $request->end_date,
                'duration' => $request->duration,
                'booking_per_slot' => $request->booking_per_slot ?? 1,
                'minimum_notice' => $request->minimum_notice ?? 0,
                'description' => $request->description,
                'location' => $request->location,
                'meet_link' => $request->meet_link,
                'physical_address' => $request->physical_address,
                'require_name' => $request->require_name ?? true,
                'require_email' => $request->require_email ?? true,
                'require_phone' => $request->require_phone ?? false,
                'custom_field' => $request->custom_field,
                'custom_field_value' => $request->custom_field_value,
                'date_override' => !empty($request->input('date_override')) ? json_encode($request->input('date_override')) : null,
            ];

            // Update calendar event with all fields
            $calendarEvent->update($updateData);

            // Handle weekly availability updates
            $calendarEvent->weeklyAvailability()->delete(); // Clear existing availability
            
            if ($request->has('availability')) {
                $weeklyAvailability = $request->input('availability');
                foreach ($weeklyAvailability as $day => $dayData) {
                    if (isset($dayData['enabled']) && $dayData['enabled']) {
                        foreach ($dayData['slots'] as $slot) {
                            EventWeeklyAvailability::create([
                                'calendar_event_id' => $calendarEvent->id,
                                'day_of_week' => $day,
                                'start_time' => $slot['start'],
                                'end_time' => $slot['end']
                            ]);
                        }
                    }
                }
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Calendar event updated successfully!',
                'data' => $calendarEvent->fresh()->load('weeklyAvailability') // Using fresh() to get updated data
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('Error updating calendar event: ' . $e->getMessage()); // Log the error
            
            return response()->json([
                'success' => false,
                'message' => 'Error updating calendar event: ' . $e->getMessage()
            ], 500);
        }
    }

    public function destroy($id)
    {
        // Check permission based on pricing plan
        if (!$this->hasCalendarEventPermission('delete calendar event')) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized - Calendar event module not available in your plan.'
            ], 403);
        }
        
        try {
            $calendarEvent = CalendarEvent::where('created_by', Auth::id())->findOrFail($id);
            $calendarEvent->delete();

            return response()->json([
                'success' => true,
                'message' => 'Calendar event deleted successfully!'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error deleting calendar event: ' . $e->getMessage()
            ], 500);
        }
    }

    public function getCalendarData(Request $request)
    {
        // Check permission based on pricing plan
        if (!$this->hasCalendarEventPermission('view calendar event')) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized - Calendar event module not available in your plan.'
            ], 403);
        }
        
        $events = CalendarEvent::where('created_by', Auth::id())
            ->with('weeklyAvailability') // Eager load the relationship
            ->get();

        $arrayJson = [];

        // Add calendar events
        foreach ($events as $event) {
            $arrayJson[] = [
                'id' => 'event_' . $event->id,
                'title' => $event->title,
                'start' => $event->start_date ? $event->start_date->format('Y-m-d H:i:s') : null,
                'end' => $event->end_date ? $event->end_date->format('Y-m-d H:i:s') : null,
                'className' => 'event-primary',
                'backgroundColor' => '#51459d',
                'borderColor' => '#51459d',
                'textColor' => '#ffffff',
                'allDay' => false,
                'extendedProps' => [
                    'type' => 'event',
                    'description' => $event->description,
                    'location' => $event->location,
                    'duration' => $event->duration,
                    'booking_per_slot' => $event->booking_per_slot,
                    'minimum_notice' => $event->minimum_notice,
                    'weekly_availability' => $event->weeklyAvailability,
                    'date_override' => json_decode($event->date_override, true)
                ]
            ];
        }

        // Add bookings from bookings table
        $bookings = \App\Models\Booking::whereHas('event', function($query) {
            $query->where('created_by', Auth::id());
        })->with('event')->get();

        foreach ($bookings as $booking) {
            $startDateTime = $booking->date . ' ' . ($booking->time ?? '00:00:00');
            $endDateTime = date('Y-m-d H:i:s', strtotime($startDateTime . ' +' . ($booking->event->duration ?? 60) . ' minutes'));

            $arrayJson[] = [
                'id' => 'booking_' . $booking->id,
                'title' => '📅 ' . $booking->name . ' - ' . $booking->event->title,
                'start' => $startDateTime,
                'end' => $endDateTime,
                'className' => 'appointment-booking',
                'backgroundColor' => '#28a745',
                'borderColor' => '#28a745',
                'textColor' => '#ffffff',
                'allDay' => false,
                'extendedProps' => [
                    'type' => 'booking',
                    'booking_id' => $booking->id,
                    'event_id' => $booking->event_id,
                    'contact_name' => $booking->name,
                    'email' => $booking->email,
                    'phone' => $booking->phone,
                    'event_title' => $booking->event->title,
                    'time' => $booking->time,
                    'custom_fields' => $booking->custom_fields,
                    'custom_fields_value' => $booking->custom_fields_value
                ]
            ];
        }

        // Add appointments from appointment_booking table
        $appointmentBookings = \App\Models\AppointmentBooking::whereHas('event', function($query) {
            $query->where('created_by', Auth::id());
        })->with('event')->get();

        foreach ($appointmentBookings as $appointmentBooking) {
            $startDateTime = $appointmentBooking->event_date . ' ' . $appointmentBooking->time_slots;
            $endDateTime = date('Y-m-d H:i:s', strtotime($startDateTime . ' +' . ($appointmentBooking->event->duration ?? 60) . ' minutes'));

            $arrayJson[] = [
                'id' => 'appointment_' . $appointmentBooking->id,
                'title' => '🗓️ Appointment - ' . $appointmentBooking->event->title,
                'start' => $startDateTime,
                'end' => $endDateTime,
                'className' => 'appointment-booking-new',
                'backgroundColor' => '#17a2b8',
                'borderColor' => '#17a2b8',
                'textColor' => '#ffffff',
                'allDay' => false,
                'extendedProps' => [
                    'type' => 'appointment_booking',
                    'appointment_id' => $appointmentBooking->id,
                    'event_id' => $appointmentBooking->event_id,
                    'event_title' => $appointmentBooking->event->title,
                    'event_location' => $appointmentBooking->event_location,
                    'event_location_value' => $appointmentBooking->event_location_value,
                    'time_zone' => $appointmentBooking->time_zone
                ]
            ];
        }

        return response()->json($arrayJson);
    }

    public function getAvailableSlots(Request $request)
    {
        $date = $request->input('date');
        // Logic to fetch available time slots for the given date
        $slots = Appointment::getAvailableSlots($date); // Adjust as per your logic
        return response()->json(['slots' => $slots]);
    }
    
    public function cancelAppointment(Request $request)
    {
        $slotId = $request->input('slot_id');
        $date = $request->input('date');
        // Logic to cancel the appointment
        $appointment = Appointment::find($slotId);
        if ($appointment) {
            $appointment->delete(); // Or mark as cancelled
            return response()->json(['success' => true]);
        }
        return response()->json(['success' => false, 'message' => 'Appointment not found.'], 404);
    }

    /**
     * Get available events for a specific date
     */
    public function getAvailableEvents(Request $request)
    {
        try {
            $date = $request->input('date');
            $userId = Auth::id();

            \Log::info('Getting available events for user: ' . $userId);

            // Build query for events that belong to the current user
            $query = CalendarEvent::where('created_by', $userId);

            // If date is provided, filter by date range
            if ($date) {
                $query->whereDate('start_date', '<=', $date)
                      ->whereDate('end_date', '>=', $date);
                \Log::info('Filtering events by date: ' . $date);
            } else {
                // If no date provided, get all future events
                $query->where('end_date', '>=', now());
                \Log::info('Getting all future events');
            }

            $events = $query->select('id', 'title', 'duration', 'location', 'physical_address', 'meet_link')
                           ->orderBy('start_date', 'asc')
                           ->get();

            \Log::info('Found ' . $events->count() . ' events');

            // If no events found, create a sample event for testing
            if ($events->isEmpty()) {
                \Log::info('No events found, creating sample event');

                $sampleEvent = CalendarEvent::create([
                    'title' => 'Sample Meeting',
                    'start_date' => now(),
                    'end_date' => now()->addDays(30),
                    'duration' => 60,
                    'booking_per_slot' => 1,
                    'minimum_notice' => 60,
                    'description' => 'Sample meeting for testing',
                    'location' => 'zoom',
                    'meet_link' => 'https://zoom.us/j/sample',
                    'created_by' => $userId,
                ]);

                $events = collect([$sampleEvent]);
                \Log::info('Created sample event with ID: ' . $sampleEvent->id);
            }

            // Add location_value to each event
            $events->each(function ($event) {
                if ($event->location === 'in_person') {
                    $event->location_value = $event->physical_address ?: 'Address not specified';
                } else {
                    $event->location_value = $event->meet_link ?: 'Link not specified';
                }
            });

            return response()->json([
                'success' => true,
                'data' => $events,
                'message' => 'Events fetched successfully'
            ]);

        } catch (\Exception $e) {
            \Log::error('Error fetching available events: ' . $e->getMessage());
            \Log::error('Stack trace: ' . $e->getTraceAsString());

            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch available events: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get available time slots for a specific event and date
     */
    public function getTimeSlots(Request $request)
    {
        try {
            $eventId = $request->input('event_id');
            $date = $request->input('date');

            if (!$eventId || !$date) {
                return response()->json([
                    'success' => false,
                    'message' => 'Event ID and date are required'
                ], 400);
            }

            // Get the event
            $event = CalendarEvent::where('id', $eventId)
                ->where('created_by', Auth::id())
                ->first();

            if (!$event) {
                return response()->json([
                    'success' => false,
                    'message' => 'Event not found'
                ], 404);
            }

            // Get existing bookings for this event and date
            $existingBookings = Booking::where('event_id', $eventId)
                ->where('date', $date)
                ->pluck('time')
                ->toArray();

            // Generate time slots based on event duration (ensure it's an integer)
            $duration = (int) ($event->duration ?: 60); // Default to 60 minutes
            $slots = [];

            // Generate slots from 9 AM to 5 PM
            $startHour = 9;
            $endHour = 17;

            for ($hour = $startHour; $hour < $endHour; $hour++) {
                for ($minute = 0; $minute < 60; $minute += $duration) {
                    if ($hour === $endHour - 1 && $minute + $duration > 60) break;

                    $timeString = sprintf('%02d:%02d', $hour, $minute);

                    // Skip if this time slot is already booked
                    if (!in_array($timeString, $existingBookings)) {
                        $displayTime = $this->formatTime12Hour($timeString);
                        $slots[] = [
                            'time' => $timeString,
                            'display_time' => $displayTime
                        ];
                    }
                }
            }

            return response()->json([
                'success' => true,
                'data' => $slots
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch time slots: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Helper method to format time to 12-hour format
     */
    private function formatTime12Hour($time24)
    {
        $time = \DateTime::createFromFormat('H:i', $time24);
        return $time->format('g:i A');
    }

    /**
     * Show the calendar view
     */
    public function showCalendarView()
    {
        // Check permission based on pricing plan
        if (!$this->hasCalendarEventPermission('view calendar event')) {
            return redirect()->back()->with('error', 'Unauthorized - Calendar event module not available in your plan.');
        }
        
        // Return the calendar events view
        return view('tasks.calendar');
    }
}
