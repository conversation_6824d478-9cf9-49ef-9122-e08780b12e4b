<?php
// Run this script to create a sample calendar event for testing
// php create_sample_event.php

require_once 'vendor/autoload.php';

use App\Models\CalendarEvent;
use App\Models\User;

// Get the first user (or specify your user ID)
$user = User::where('type', 'company')->first();

if (!$user) {
    echo "No company user found. Please create a user first.\n";
    exit;
}

// Create a sample calendar event
$event = CalendarEvent::create([
    'title' => 'Sample Meeting',
    'start_date' => now(),
    'end_date' => now()->addDays(30),
    'duration' => 60,
    'booking_per_slot' => 1,
    'minimum_notice' => 60,
    'description' => 'Sample meeting for testing appointment booking',
    'location' => 'zoom',
    'meet_link' => 'https://zoom.us/j/sample123',
    'physical_address' => null,
    'require_name' => true,
    'require_email' => true,
    'require_phone' => false,
    'created_by' => $user->id,
]);

echo "Sample calendar event created successfully!\n";
echo "Event ID: " . $event->id . "\n";
echo "Title: " . $event->title . "\n";
echo "Created for user: " . $user->name . " (ID: " . $user->id . ")\n";
